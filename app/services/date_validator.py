from dataclasses import dataclass
import logging
from typing import cast

from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from repositories import OpenAIRepository
from schemas.dates import DatesLLMResponse


__all__ = ['DateValidatorService']


logger = logging.getLogger(__name__)


SYSTEM_PROMPT = """Context: the messages you will accept are related to some date clarification users see on their screen. It can contain two dates, one date or no dates at all.

Role: You are a specialized data extraction tool for temporal information.

Objective: Your task is to scan for date components (day/month/year), validate their logical consistency, assemble into complete dates, convert to ISO format and return them in a valid json format.

Components of the date:

1. date - can be a number or a word, it is a number from 1 to 31. Examples: “29”, “3rd”, “third”
2. Month - can be a number from 1 and 12 or a word. Examples: “06” “Jun”, “August”
3. Year - is a number. Examples: “25” “2024” etc

The date can also come in one bit: here are examples of the same date: “1st June 2025”, “1.06.2025”, “1.06.25”, “June 1 2025”, “06/01/2025”, “2025-06-01”

Steps: The thought process will follow such path: you scan the message from the beginning, you must pick up components for the first date. You must find all three components: day, month and year and save the date to the key: “date_1”. Then you continue to look for the next date following the same logic. After finding all the components for the next date you save it to the key: “date_2”.

Return dates in strings.  If one of the dates is empty, return “null”.

Here is the format to return the dates

{
"date_1": "string",
"date_2": "string"
}
"""

USER_PROMPT = """Here is a user  message for you to process: {user_message}. Think step by step, detect dates, convert them to ISO and return your results in JSON format."""

# response = client.chat.completions.create(
# model=AZURE_OPENAI_MODEL,
# response_format={"type": "json_object"},
# messages=[
#     {"role": "system", "content": SYSTEM_PROMPT},
#     {"role": "user", "content": USER_PROMPT}
# ],
# temperature=0.0
# )


@dataclass(frozen=True)
class DateValidatorService:
    """Validates and extracts date information from user messages."""

    openai_service: OpenAIRepository
    temperature: float = settings.openai.default_temperature

    def _get_user_prompt(
        self,
        *,
        user_message: str,
    ) -> str:
        return USER_PROMPT.format(
            user_message=user_message,
        )

    def _get_system_prompt(
        self,
    ) -> str:
        return SYSTEM_PROMPT

    @staticmethod
    def _create_system_message(message: str) -> ChatCompletionSystemMessageParam:
        """Create a system message for the chat completion."""
        return {'role': 'system', 'content': message}

    @staticmethod
    def _create_user_message(message: str) -> ChatCompletionUserMessageParam:
        """Create a user message for the chat completion."""
        return {'role': 'user', 'content': message}

    async def validate_dates(
        self,
        user_message: str,
    ) -> DatesLLMResponse:
        """
        Validate the user's message dates using an LLM.

        Args:
            user_message: The user's message content.

        Returns:
            The validated dates response containing extracted date information.
        """

        # Prepare the messages
        system_message = self._create_system_message(self._get_system_prompt())
        user_message_param = self._create_user_message(
            self._get_user_prompt(
                user_message=user_message,
            )
        )

        # Call the OpenAI API
        response = await self.openai_service.generate_chat_completion(
            messages=[
                cast(ChatCompletionMessageParam, system_message),
                cast(ChatCompletionMessageParam, user_message_param),
            ],
            temperature=self.temperature,
            response_format=DatesLLMResponse,
        )

        if not isinstance(response, DatesLLMResponse):
            raise RuntimeError(f'Invalid response from model, expected {DatesLLMResponse}, got {type(response)}')

        return response
