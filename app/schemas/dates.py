from datetime import datetime

from pydantic import BaseModel, field_validator


class DatesLLMResponse(BaseModel):
    date_1: datetime | None = None
    date_2: datetime | None = None

    @field_validator('date_1', 'date_2', mode='before')
    @classmethod
    def parse_date_str(cls, value: str) -> datetime | None:
        if not value:
            return None
        try:
            return datetime.fromisoformat(value)
        except ValueError:
            raise ValueError(f'{value} date must be a valid date in ISO format')
